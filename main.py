from flask import Flask, jsonify, request, render_template
import json, os

DATA_FILE = 'data.json'

app = Flask(__name__, template_folder="templates", static_folder='static')

# Validar si existen datos
def validarData():
    if os.path.exists(DATA_FILE):
        with open(DATA_FILE, 'r') as f:
            try:
                return json.load(f)
            except json.JSONDecodeError:
                return []  # Retorna lista vacía si el JSON está vacío o corrupto
    return []  # Devuelve lista vacía si no existe el archivo

@app.route('/')
def inicio():
    return render_template('index.html')

# Endpoint para obtener todos los dragones
@app.route('/data', methods=['GET'])
def getDragones():
    data = validarData()
    return jsonify(data)

# Endpoint para agregar un nuevo dragón
@app.route('/dragon', methods=['POST'])
def agregar_dragon():
    data_actual = validarData()
    nuevo_dragon = request.get_json()
    
    if data_actual:
        nuevo_id = max(d['id'] for d in data_actual) + 1
    else:
        nuevo_id = 1

    nuevo_dragon['id'] = nuevo_id
    data_actual.append(nuevo_dragon)

    with open(DATA_FILE, 'w') as f:
        json.dump(data_actual, f, indent=4)

    return jsonify({'mensaje': 'Dragón agregado', 'dragon': nuevo_dragon}), 201

# Endpoint para obtener un dragón por ID
@app.route('/dragon/<int:id>', methods=['GET'])
def getdragon(id):
    data = validarData()
    dragon = next((m for m in data if m['id'] == id), None)
    if dragon:
        return jsonify(dragon)
    return jsonify({'mensaje': 'Tu dragón no existe'}), 404

# Endpoint para actualizar un dragón (PUT)
@app.route('/dragon/<int:id>', methods=['PUT'])
def actualizar_dragon(id):
    data_actual = validarData()
    dragon_index = next((i for i, d in enumerate(data_actual) if d['id'] == id), None)

    if dragon_index is None:
        return jsonify({'mensaje': 'Dragón no encontrado'}), 404

    datos_actualizados = request.get_json()

    # Validar que se envíen los datos requeridos
    if not datos_actualizados:
        return jsonify({'mensaje': 'No se enviaron datos para actualizar'}), 400

    # Mantener el ID original
    datos_actualizados['id'] = id

    # Actualizar el dragón en la lista
    data_actual[dragon_index] = datos_actualizados

    # Guardar los cambios
    with open(DATA_FILE, 'w') as f:
        json.dump(data_actual, f, indent=4)

    return jsonify({'mensaje': 'Dragón actualizado exitosamente', 'dragon': datos_actualizados}), 200

# Endpoint para eliminar un dragón (DELETE)
@app.route('/dragon/<int:id>', methods=['DELETE'])
def eliminar_dragon(id):
    data_actual = validarData()
    dragon_index = next((i for i, d in enumerate(data_actual) if d['id'] == id), None)

    if dragon_index is None:
        return jsonify({'mensaje': 'Dragón no encontrado'}), 404

    # Obtener información del dragón antes de eliminarlo
    dragon_eliminado = data_actual[dragon_index]

    # Eliminar el dragón de la lista
    data_actual.pop(dragon_index)

    # Guardar los cambios
    with open(DATA_FILE, 'w') as f:
        json.dump(data_actual, f, indent=4)

    return jsonify({
        'mensaje': f'Dragón "{dragon_eliminado["nombre"]}" eliminado exitosamente',
        'dragon_eliminado': dragon_eliminado
    }), 200

# Solo un bloque para ejecutar la app
if __name__ == '__main__':
    app.run(debug=True)
