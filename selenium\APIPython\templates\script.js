let dragons = [];

fetch('data/dragons.json')
    .then(response => {
        if (!response.ok) {
            throw new Error('Error al cargar el archivo JSON');
        }
        return response.json();
    })
    .then(data => {
        dragons = data;
        console.log('Datos cargados:', dragons); // Para depuración
    })
    .catch(error => {
        console.error('Error:', error);
        // Opcional: mostrar un mensaje en la interfaz
        document.querySelector('.dragon-grid').innerHTML = '<p>Error al cargar los datos de los dragones.</p>';
    });

function selectDragon(id) {
    const dragon = dragons.find(d => d.id === id);
    if (dragon) {
        document.getElementById(`dragon-name-${id}`).textContent = `Nombre: ${dragon.nombre}`;
        document.getElementById(`dragon-deaths-${id}`).textContent = `Muertes: ${dragon.Muertes}`;
        document.getElementById(`dragon-place-${id}`).textContent = `Lugar: ${dragon.Lugar}`;

        document.querySelectorAll('.dragon-card').forEach(card => card.classList.remove('selected'));
        document.querySelector(`.dragon-card[data-id="${id}"]`).classList.add('selected');
    }
}