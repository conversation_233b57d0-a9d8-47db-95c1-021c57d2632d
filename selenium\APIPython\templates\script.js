let dragons = [];

// Función para crear una tarjeta de dragón
function crearTarjetaDragon(dragon) {
    const imageUrls = [
        'https://images.alphacoders.com/114/1149159.jpg',
        'https://images.alphacoders.com/114/1149160.jpg',
        'https://images.alphacoders.com/114/1149161.jpg',
        'https://wallpaperaccess.com/full/1208400.jpg',
        'https://wallpaperaccess.com/full/1208401.jpg'
    ];

    const randomImage = imageUrls[Math.floor(Math.random() * imageUrls.length)];

    return `
        <div class="dragon-card" style="background-image: url('${randomImage}');" data-id="${dragon.id}" onclick="selectDragon(${dragon.id})">
            <div class="rune-glow"></div>
            <div class="dragon-content">
                <h2 class="text-2xl font-bold">${dragon.nombre}</h2>
                <p>Un dragón legendario de ${dragon.Lugar}, con ${dragon.Muertes} muertes registradas.</p>
                <div class="dragon-info">
                    <p id="dragon-name-${dragon.id}">Nombre: -</p>
                    <p id="dragon-deaths-${dragon.id}">Muertes: -</p>
                    <p id="dragon-place-${dragon.id}">Lugar: -</p>
                </div>
            </div>
        </div>
    `;
}

// Función para renderizar todos los dragones
function renderizarDragones() {
    const dragonGrid = document.querySelector('.dragon-grid');
    dragonGrid.innerHTML = dragons.map(dragon => crearTarjetaDragon(dragon)).join('');
}

// Cargar datos desde la API Flask
function cargarDragones() {
    return fetch('/data')
        .then(response => {
            if (!response.ok) {
                throw new Error('Error al cargar los datos desde la API');
            }
            return response.json();
        })
        .then(data => {
            dragons = data;
            console.log('Datos cargados desde API:', dragons);
            renderizarDragones();
        })
        .catch(error => {
            console.error('Error:', error);
            document.querySelector('.dragon-grid').innerHTML = '<p>Error al cargar los datos de los dragones.</p>';
        });
}

// Cargar dragones al inicio
cargarDragones();

function selectDragon(id) {
    const dragon = dragons.find(d => d.id === id);
    if (dragon) {
        document.getElementById(`dragon-name-${id}`).textContent = `Nombre: ${dragon.nombre}`;
        document.getElementById(`dragon-deaths-${id}`).textContent = `Muertes: ${dragon.Muertes}`;
        document.getElementById(`dragon-place-${id}`).textContent = `Lugar: ${dragon.Lugar}`;

        document.querySelectorAll('.dragon-card').forEach(card => card.classList.remove('selected'));
        document.querySelector(`.dragon-card[data-id="${id}"]`).classList.add('selected');
    }
}

// Función para agregar un nuevo dragón
function agregarDragon(dragonData) {
    return fetch('/dragon', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(dragonData)
    })
    .then(response => {
        if (!response.ok) {
            throw new Error('Error al agregar el dragón');
        }
        return response.json();
    });
}

// Función para recargar los datos de dragones
function recargarDragones() {
    return fetch('/data')
        .then(response => {
            if (!response.ok) {
                throw new Error('Error al recargar los datos');
            }
            return response.json();
        })
        .then(data => {
            dragons = data;
            console.log('Datos recargados:', dragons);
            renderizarDragones(); // Renderizar los dragones actualizados
        });
}

// Manejar el envío del formulario
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('dragonForm');

    form.addEventListener('submit', function(e) {
        e.preventDefault();

        const formData = new FormData(form);
        const dragonData = {
            nombre: formData.get('nombre'),
            Muertes: parseInt(formData.get('muertes')),
            Lugar: formData.get('lugar')
        };

        agregarDragon(dragonData)
            .then(response => {
                // Mostrar mensaje de éxito
                mostrarMensaje(`¡Dragón "${response.dragon.nombre}" agregado exitosamente!`, 'success');
                form.reset();
                return recargarDragones();
            })
            .then(() => {
                console.log('Lista de dragones actualizada');
                // Hacer scroll hacia el nuevo dragón
                setTimeout(() => {
                    const nuevoDragon = document.querySelector(`[data-id="${dragons[dragons.length - 1].id}"]`);
                    if (nuevoDragon) {
                        nuevoDragon.scrollIntoView({ behavior: 'smooth', block: 'center' });
                        nuevoDragon.classList.add('nuevo-dragon');
                        setTimeout(() => nuevoDragon.classList.remove('nuevo-dragon'), 3000);
                    }
                }, 100);
            })
            .catch(error => {
                console.error('Error:', error);
                mostrarMensaje('Error al agregar el dragón: ' + error.message, 'error');
            });
    });
});

// Función para mostrar mensajes
function mostrarMensaje(mensaje, tipo) {
    const mensajeDiv = document.createElement('div');
    mensajeDiv.className = `mensaje ${tipo}`;
    mensajeDiv.textContent = mensaje;

    document.body.appendChild(mensajeDiv);

    setTimeout(() => {
        mensajeDiv.classList.add('mostrar');
    }, 100);

    setTimeout(() => {
        mensajeDiv.classList.remove('mostrar');
        setTimeout(() => {
            document.body.removeChild(mensajeDiv);
        }, 300);
    }, 3000);
}