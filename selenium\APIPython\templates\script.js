let dragons = [];

// Cargar datos desde la API Flask
fetch('/data')
    .then(response => {
        if (!response.ok) {
            throw new Error('Error al cargar los datos desde la API');
        }
        return response.json();
    })
    .then(data => {
        dragons = data;
        console.log('Datos cargados desde API:', dragons); // Para depuración
    })
    .catch(error => {
        console.error('Error:', error);
        // Opcional: mostrar un mensaje en la interfaz
        document.querySelector('.dragon-grid').innerHTML = '<p>Error al cargar los datos de los dragones.</p>';
    });

function selectDragon(id) {
    const dragon = dragons.find(d => d.id === id);
    if (dragon) {
        document.getElementById(`dragon-name-${id}`).textContent = `Nombre: ${dragon.nombre}`;
        document.getElementById(`dragon-deaths-${id}`).textContent = `Muertes: ${dragon.Muertes}`;
        document.getElementById(`dragon-place-${id}`).textContent = `Lugar: ${dragon.Lugar}`;

        document.querySelectorAll('.dragon-card').forEach(card => card.classList.remove('selected'));
        document.querySelector(`.dragon-card[data-id="${id}"]`).classList.add('selected');
    }
}

// Función para agregar un nuevo dragón
function agregarDragon(dragonData) {
    return fetch('/dragon', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(dragonData)
    })
    .then(response => {
        if (!response.ok) {
            throw new Error('Error al agregar el dragón');
        }
        return response.json();
    });
}

// Función para recargar los datos de dragones
function recargarDragones() {
    return fetch('/data')
        .then(response => {
            if (!response.ok) {
                throw new Error('Error al recargar los datos');
            }
            return response.json();
        })
        .then(data => {
            dragons = data;
            console.log('Datos recargados:', dragons);
        });
}

// Manejar el envío del formulario
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('dragonForm');

    form.addEventListener('submit', function(e) {
        e.preventDefault();

        const formData = new FormData(form);
        const dragonData = {
            nombre: formData.get('nombre'),
            Muertes: parseInt(formData.get('muertes')),
            Lugar: formData.get('lugar')
        };

        agregarDragon(dragonData)
            .then(response => {
                alert(`¡Dragón "${response.dragon.nombre}" agregado exitosamente!`);
                form.reset();
                return recargarDragones();
            })
            .then(() => {
                console.log('Lista de dragones actualizada');
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Error al agregar el dragón: ' + error.message);
            });
    });
});