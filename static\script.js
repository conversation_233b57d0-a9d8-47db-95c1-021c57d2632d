let dragons = [];

// Función para crear una tarjeta de dragón
function crearTarjetaDragon(dragon) {
    const imageUrls = [
        'https://images.alphacoders.com/114/1149159.jpg',
        'https://images.alphacoders.com/114/1149160.jpg',
        'https://images.alphacoders.com/114/1149161.jpg',
        'https://wallpaperaccess.com/full/1208400.jpg',
        'https://wallpaperaccess.com/full/1208401.jpg'
    ];

    const randomImage = imageUrls[Math.floor(Math.random() * imageUrls.length)];

    return `
        <div class="dragon-card" style="background-image: url('${randomImage}');" data-id="${dragon.id}" onclick="selectDragon(${dragon.id})">
            <div class="rune-glow"></div>
            <div class="dragon-actions">
                <button class="action-btn edit-btn" onclick="event.stopPropagation(); editarDragon(${dragon.id})" title="Editar dragón">
                    ✏️
                </button>
                <button class="action-btn delete-btn" onclick="event.stopPropagation(); eliminarDragon(${dragon.id})" title="Eliminar dragón">
                    🗑️
                </button>
            </div>
            <div class="dragon-content">
                <h2 class="text-2xl font-bold">${dragon.nombre}</h2>
                <p>Un dragón legendario de ${dragon.Lugar}, con ${dragon.Muertes} muertes registradas.</p>
                <div class="dragon-info">
                    <p id="dragon-name-${dragon.id}">Nombre: -</p>
                    <p id="dragon-deaths-${dragon.id}">Muertes: -</p>
                    <p id="dragon-place-${dragon.id}">Lugar: -</p>
                </div>
            </div>
        </div>
    `;
}

// Función para renderizar todos los dragones
function renderizarDragones() {
    console.log('Renderizando dragones:', dragons);
    const dragonGrid = document.querySelector('.dragon-grid');

    if (!dragonGrid) {
        console.error('No se encontró el contenedor .dragon-grid');
        return;
    }

    if (dragons.length === 0) {
        dragonGrid.innerHTML = '<p class="no-dragons">No hay dragones registrados aún.</p>';
        return;
    }

    const dragonCards = dragons.map(dragon => {
        console.log('Creando tarjeta para dragón:', dragon);
        return crearTarjetaDragon(dragon);
    }).join('');

    dragonGrid.innerHTML = dragonCards;
    console.log('Dragones renderizados exitosamente. Total:', dragons.length);
}

// Cargar datos desde la API Flask
function cargarDragones() {
    console.log('Iniciando carga de dragones...');

    return fetch('/data')
        .then(response => {
            console.log('Respuesta recibida:', response.status, response.statusText);
            if (!response.ok) {
                throw new Error(`Error HTTP: ${response.status} - ${response.statusText}`);
            }
            return response.json();
        })
        .then(data => {
            console.log('Datos JSON recibidos:', data);
            dragons = data;
            console.log('Dragones asignados a variable global:', dragons);
            renderizarDragones();
        })
        .catch(error => {
            console.error('Error completo:', error);
            const dragonGrid = document.querySelector('.dragon-grid');
            dragonGrid.innerHTML = `
                <div class="error-message">
                    <p>❌ Error al cargar los dragones</p>
                    <p>Detalles: ${error.message}</p>
                    <button onclick="cargarDragones()" class="retry-btn">🔄 Reintentar</button>
                </div>
            `;
        });
}

// Cargar dragones al inicio
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM cargado, iniciando carga de dragones...');
    cargarDragones();
});

function selectDragon(id) {
    const dragon = dragons.find(d => d.id === id);
    if (dragon) {
        document.getElementById(`dragon-name-${id}`).textContent = `Nombre: ${dragon.nombre}`;
        document.getElementById(`dragon-deaths-${id}`).textContent = `Muertes: ${dragon.Muertes}`;
        document.getElementById(`dragon-place-${id}`).textContent = `Lugar: ${dragon.Lugar}`;

        document.querySelectorAll('.dragon-card').forEach(card => card.classList.remove('selected'));
        document.querySelector(`.dragon-card[data-id="${id}"]`).classList.add('selected');
    }
}

// Función para agregar un nuevo dragón
function agregarDragon(dragonData) {
    return fetch('/dragon', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(dragonData)
    })
    .then(response => {
        if (!response.ok) {
            throw new Error('Error al agregar el dragón');
        }
        return response.json();
    });
}

// Función para recargar los datos de dragones
function recargarDragones() {
    return fetch('/data')
        .then(response => {
            if (!response.ok) {
                throw new Error('Error al recargar los datos');
            }
            return response.json();
        })
        .then(data => {
            dragons = data;
            console.log('Datos recargados:', dragons);
            renderizarDragones(); // Renderizar los dragones actualizados
        });
}

// Manejar el envío del formulario
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('dragonForm');

    form.addEventListener('submit', function(e) {
        e.preventDefault();

        const formData = new FormData(form);
        const dragonData = {
            nombre: formData.get('nombre'),
            Muertes: parseInt(formData.get('muertes')),
            Lugar: formData.get('lugar')
        };

        agregarDragon(dragonData)
            .then(response => {
                // Mostrar mensaje de éxito
                mostrarMensaje(`¡Dragón "${response.dragon.nombre}" agregado exitosamente!`, 'success');
                form.reset();
                return recargarDragones();
            })
            .then(() => {
                console.log('Lista de dragones actualizada');
                // Hacer scroll hacia el nuevo dragón
                setTimeout(() => {
                    const nuevoDragon = document.querySelector(`[data-id="${dragons[dragons.length - 1].id}"]`);
                    if (nuevoDragon) {
                        nuevoDragon.scrollIntoView({ behavior: 'smooth', block: 'center' });
                        nuevoDragon.classList.add('nuevo-dragon');
                        setTimeout(() => nuevoDragon.classList.remove('nuevo-dragon'), 3000);
                    }
                }, 100);
            })
            .catch(error => {
                console.error('Error:', error);
                mostrarMensaje('Error al agregar el dragón: ' + error.message, 'error');
            });
    });
});

// Función para mostrar mensajes
function mostrarMensaje(mensaje, tipo) {
    const mensajeDiv = document.createElement('div');
    mensajeDiv.className = `mensaje ${tipo}`;
    mensajeDiv.textContent = mensaje;

    document.body.appendChild(mensajeDiv);

    setTimeout(() => {
        mensajeDiv.classList.add('mostrar');
    }, 100);

    setTimeout(() => {
        mensajeDiv.classList.remove('mostrar');
        setTimeout(() => {
            document.body.removeChild(mensajeDiv);
        }, 300);
    }, 3000);
}

// Función para actualizar un dragón (PUT)
function actualizarDragon(id, dragonData) {
    return fetch(`/dragon/${id}`, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(dragonData)
    })
    .then(response => {
        if (!response.ok) {
            throw new Error('Error al actualizar el dragón');
        }
        return response.json();
    });
}

// Función para eliminar un dragón (DELETE)
function eliminarDragonAPI(id) {
    return fetch(`/dragon/${id}`, {
        method: 'DELETE'
    })
    .then(response => {
        if (!response.ok) {
            throw new Error('Error al eliminar el dragón');
        }
        return response.json();
    });
}

// Función para editar un dragón
function editarDragon(id) {
    const dragon = dragons.find(d => d.id === id);
    if (!dragon) {
        mostrarMensaje('Dragón no encontrado', 'error');
        return;
    }

    // Crear modal de edición
    const modal = document.createElement('div');
    modal.className = 'modal-overlay';
    modal.innerHTML = `
        <div class="modal-content">
            <h2>Editar Dragón</h2>
            <form id="editForm">
                <div class="form-group">
                    <label for="editNombre">Nombre:</label>
                    <input type="text" id="editNombre" name="nombre" value="${dragon.nombre}" required>
                </div>
                <div class="form-group">
                    <label for="editMuertes">Muertes:</label>
                    <input type="number" id="editMuertes" name="muertes" value="${dragon.Muertes}" required>
                </div>
                <div class="form-group">
                    <label for="editLugar">Lugar:</label>
                    <input type="text" id="editLugar" name="lugar" value="${dragon.Lugar}" required>
                </div>
                <div class="modal-buttons">
                    <button type="submit" class="submit-btn">Actualizar</button>
                    <button type="button" class="cancel-btn" onclick="cerrarModal()">Cancelar</button>
                </div>
            </form>
        </div>
    `;

    document.body.appendChild(modal);

    // Manejar envío del formulario de edición
    document.getElementById('editForm').addEventListener('submit', function(e) {
        e.preventDefault();

        const formData = new FormData(e.target);
        const dragonData = {
            nombre: formData.get('nombre'),
            Muertes: parseInt(formData.get('muertes')),
            Lugar: formData.get('lugar')
        };

        actualizarDragon(id, dragonData)
            .then(response => {
                mostrarMensaje(`¡Dragón "${response.dragon.nombre}" actualizado exitosamente!`, 'success');
                cerrarModal();
                return recargarDragones();
            })
            .catch(error => {
                console.error('Error:', error);
                mostrarMensaje('Error al actualizar el dragón: ' + error.message, 'error');
            });
    });
}

// Función para eliminar un dragón
function eliminarDragon(id) {
    const dragon = dragons.find(d => d.id === id);
    if (!dragon) {
        mostrarMensaje('Dragón no encontrado', 'error');
        return;
    }

    if (confirm(`¿Estás seguro de que quieres eliminar al dragón "${dragon.nombre}"?`)) {
        eliminarDragonAPI(id)
            .then(response => {
                mostrarMensaje(response.mensaje, 'success');
                return recargarDragones();
            })
            .catch(error => {
                console.error('Error:', error);
                mostrarMensaje('Error al eliminar el dragón: ' + error.message, 'error');
            });
    }
}

// Función para cerrar modal
function cerrarModal() {
    const modal = document.querySelector('.modal-overlay');
    if (modal) {
        document.body.removeChild(modal);
    }
}