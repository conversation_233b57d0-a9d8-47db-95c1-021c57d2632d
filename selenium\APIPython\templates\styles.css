body {
    font-family: 'IM Fell English SC', serif;
    color: #f4e8c1;
    margin: 0;
    background: linear-gradient(to bottom, #1a1a2e, #16213e);
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    align-items: center;
}
.dragon-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
    width: 100%;
    max-width: 1600px;
    padding: 2rem;
}
.dragon-card {
    position: relative;
    height: 600px;
    background-size: cover;
    background-position: center;
    border-radius: 12px;
    cursor: pointer;
    transition: transform 0.5s ease-in-out, box-shadow 0.5s;
    border: 6px solid #3c2f2f;
    border-image: url('https://www.transparenttextures.com/patterns/metal.png') 30 stretch;
    border-image-slice: 30;
}
.dragon-card:hover {
    transform: scale(1.05);
    box-shadow: 0 12px 24px rgba(0, 0, 0, 0.7);
}
.dragon-card.selected {
    box-shadow: inset 0 0 50px rgba(212, 160, 23, 0.7), 0 8px 16px rgba(0, 0, 0, 0.5);
}
.dragon-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1;
}
.dragon-content {
    position: absolute;
    bottom: 1.5rem;
    left: 1.5rem;
    right: 1.5rem;
    z-index: 2;
    background-image: url('https://www.transparenttextures.com/patterns/old-parchment.png');
    background-color: rgba(228, 208, 167, 0.9);
    padding: 1.5rem;
    border-radius: 8px;
    border: 3px solid #3c2f2f;
    transition: height 0.3s ease-in-out;
}
.dragon-content h2 {
    font-family: 'Uncial Antiqua', cursive;
    color: #d4a017;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7);
    font-size: 2.5rem;
    margin-bottom: 0.5rem;
}
.dragon-content p {
    font-size: 1rem;
    color: #3c2005;
    margin: 0.25rem 0;
}
.dragon-content .dragon-info {
    display: none;
}
.dragon-card.selected .dragon-content .dragon-info {
    display: block;
}
.rune-glow {
    position: absolute;
    top: 10px;
    right: 10px;
    width: 40px;
    height: 40px;
    background: radial-gradient(circle, rgba(212, 160, 23, 0.8), transparent);
    z-index: 2;
    border-radius: 50%;
}
h1 {
    font-family: 'Uncial Antiqua', cursive;
    color: #d4a017;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7);
}

/* Estilos para el formulario */
.form-container {
    background: rgba(228, 208, 167, 0.9);
    border: 3px solid #3c2f2f;
    border-radius: 12px;
    padding: 2rem;
    margin: 2rem auto;
    max-width: 500px;
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.5);
}

.form-container h2 {
    font-family: 'Uncial Antiqua', cursive;
    color: #d4a017;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7);
    margin-bottom: 1rem;
}

.dragon-form {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group label {
    color: #3c2005;
    font-weight: bold;
    margin-bottom: 0.5rem;
    font-size: 1.1rem;
}

.form-group input {
    padding: 0.75rem;
    border: 2px solid #3c2f2f;
    border-radius: 6px;
    background: rgba(255, 255, 255, 0.9);
    color: #3c2005;
    font-size: 1rem;
    transition: border-color 0.3s;
}

.form-group input:focus {
    outline: none;
    border-color: #d4a017;
    box-shadow: 0 0 5px rgba(212, 160, 23, 0.5);
}

.submit-btn {
    background: linear-gradient(45deg, #d4a017, #f4e8c1);
    color: #3c2005;
    border: 2px solid #3c2f2f;
    padding: 1rem 2rem;
    border-radius: 8px;
    font-size: 1.1rem;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s;
    margin-top: 1rem;
}

.submit-btn:hover {
    background: linear-gradient(45deg, #f4e8c1, #d4a017);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}
@media (max-width: 1024px) {
    .dragon-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
        padding: 1.5rem;
    }
    .dragon-card {
        height: 500px;
    }
    .dragon-content h2 {
        font-size: 2rem;
    }
}
@media (max-width: 640px) {
    .dragon-card {
        height: 400px;
    }
    .dragon-content {
        padding: 1rem;
    }
    .dragon-content h2 {
        font-size: 1.8rem;
    }
    .dragon-content p {
        font-size: 0.9rem;
    }
}